package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.common.integration.cards.CinemaxCardsConnector
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.PurchasableSkuResponse
import com.cleevio.cinemax.api.module.card.exception.CardProductListingFailedException
import com.cleevio.cinemax.api.module.card.service.command.ListPurchasableCardProductsCommand

import com.cleevio.cinemax.api.module.product.service.CreateCardProductService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.command.CreateCardProductCommand
import com.cleevio.cinemax.api.module.product.service.model.CardProductModel
import com.cleevio.cinemax.api.module.product.service.model.toModel
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ListPurchasableCardProductsService(
    private val cardJpaFinderService: CardJpaFinderService,
    private val cinemaxCardsConnector: CinemaxCardsConnector,
    private val createCardProductService: CreateCardProductService,
    private val productJpaFinderService: ProductJpaFinderService,
) {

    operator fun invoke(
        @Valid command: ListPurchasableCardProductsCommand
    ): List<CardProductModel> {
        val card = cardJpaFinderService.getById(command.cardId)

        val purchasableProducts = fetchPurchasableProducts(cardCode = card.code)

        val originalIds = purchasableProducts.map { it.originalId }.toSet()
        val existingProductsMap = productJpaFinderService
            .findAllByOriginalIdInAndDeletedAtIsNull(originalIds)
            .associateBy { it.originalId!! }

        val cardProductModels = purchasableProducts.map { purchasableProduct ->

            // get existing card product or create new one
            existingProductsMap[purchasableProduct.originalId]
                ?.toModel()
                ?: run {
                    val createdProductId = createCardProductService.create(
                        command = purchasableProduct.toCommand(card.id)
                    )

                    productJpaFinderService
                        .getNonDeletedById(createdProductId)
                        .toModel()
                }
        }

        return cardProductModels
    }

    private fun fetchPurchasableProducts(cardCode: String): ListPurchasableSkusResponse = runCatching {
        cinemaxCardsConnector.listPurchasableSkus(cardCode = cardCode)
    }.getOrElse {
        throw CardProductListingFailedException()
    }

    private fun PurchasableSkuResponse.toCommand(cardId: UUID) = CreateCardProductCommand(
        cardId = cardId,
        productInput = CreateCardProductCommand.CardProductInput(
            originalId = originalId,
            country = country,
            currency = currency,
            price = price,
            type = type,
            instanceValidity = instanceValidity,
        )
    )
}
