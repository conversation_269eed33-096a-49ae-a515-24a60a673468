package com.cleevio.cinemax.api.module.card.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.module.card.controller.dto.CardProductResponse
import com.cleevio.cinemax.api.module.card.controller.mapper.CardProductResponseMapper
import com.cleevio.cinemax.api.module.card.service.ListPurchasableCardProductsService
import com.cleevio.cinemax.api.module.card.service.command.ListPurchasableCardProductsCommand
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Tag(name = "POS Cards")
@RestController
@RequestMapping("/pos-app/cards")
class CardController(
    private val listPurchasableCardProductsService: ListPurchasableCardProductsService,
    private val cardProductResponseMapper: CardProductResponseMapper,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{cardCode}/purchasable-products", produces = [ApiVersion.VERSION_1_JSON])
    fun listPurchasableCardProducts(
        @PathVariable cardCode: String,
    ): List<CardProductResponse> = listPurchasableCardProductsService(
        command = ListPurchasableCardProductsCommand(cardCode = cardCode)
    ).let { products -> cardProductResponseMapper.mapList(products) }

}
