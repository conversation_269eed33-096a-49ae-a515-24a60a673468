package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.Language
import com.cleevio.cinemax.api.common.constant.PRODUCT
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.common.util.subtractTax
import com.cleevio.cinemax.api.common.util.getLocalizedText
import com.cleevio.cinemax.api.module.card.service.CardJpaFinderService
import com.cleevio.cinemax.api.module.product.constant.ProductLockValues.CREATE_OR_UPDATE_PRODUCT
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.command.CreateCardProductCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateCardProductService(
    private val cardJpaFinderService: CardJpaFinderService,
    private val productRepository: ProductRepository,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
) {

    @Lock(PRODUCT, CREATE_OR_UPDATE_PRODUCT)
    @Transactional
    fun create(
        @Valid
        @LockFieldParameter("cardId") // TODO ok locking?
        command: CreateCardProductCommand,
    ): UUID {
        val language = Language.fromBusinessCountry(cinemaxConfigProperties.businessCountry)
        val standardTaxRate = cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD)

        val cardProductCategory = productCategoryJpaFinderService.getByType(ProductCategoryType.CARD)
        val card = cardJpaFinderService.getById(command.cardId)

        with(command.productInput) {
            val productCode = "${country}${originalId}"

            val product = Product(
                originalId = originalId,
                code = productCode,
                originalCode = null,
                productCategoryId = cardProductCategory.id,
                title = "${card.title} (${instanceValidity.getLocalizedText(language)})",
                type = ProductType.PRODUCT,
                price = price,
                flagshipPrice = null,
                priceNoVat = price.subtractTax(standardTaxRate),
                active = true,
                order = null,
                soldInBuffet = true,
                soldInCafe = true,
                soldInVip = true,
                isPackagingDeposit = false
            )
            return productRepository.save(product).id
        }
    }
}
